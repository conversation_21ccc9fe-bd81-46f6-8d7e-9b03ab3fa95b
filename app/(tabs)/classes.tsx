import React, { useState } from "react";
import { VStack } from "@/components/ui/vstack";
import { SafeAreaView } from "react-native-safe-area-context";
import ClassesHeader from "@/components/screens/classes/classes-header";
import HorizontalDatePicker from "@/components/shared/horizontal-date-picker";
import ClassesTabs from "@/components/screens/classes/classes-tabs";
import { ClassCardSkeleton } from "@/components/screens/classes/class-card/class-skeleton";

import { SearchInput } from "@/components/screens/classes/classes-header/search";
import { FlatList, Share } from "react-native";

import { formatDate } from "@/data/common/common.utils";
import { ClassCard } from "@/components/screens/classes/class-card/class-card";

import { EmptyState } from "@/components/screens/classes/empty-state";
import { EmptySearchIcon } from "@/components/shared/icon/empty-search";

import { matchSorter } from "match-sorter";
import { Button, ButtonText } from "@/components/ui/button";
import { useClassesQuery } from "@/data/screens/classes/queries/useClassesQuery";
import {
  useReservationFeedback,
  ReservationErrorAlert,
  ReservationSuccessBottomSheet,
} from "@/components/screens/classes/reservation-feedback";

const RenderEmptyState = ({
  isLoading,
  isEmpty,
  searchTerm,
  setSearchTerm,
}: {
  isLoading: boolean;
  isEmpty: boolean;
  searchTerm?: string;
  setSearchTerm: (text: string) => void;
}) => {
  if (isLoading) {
    return (
      <FlatList
        data={skeletonData}
        renderItem={() => <ClassCardSkeleton />}
        keyExtractor={(item) => String(item.id)}
        showsVerticalScrollIndicator={false}
      />
    );
  }

  if (searchTerm && isEmpty) {
    return (
      <EmptyState
        subtitle="No class matched the search criteria you entered"
        title="No result found"
        icon={<EmptySearchIcon />}
        action={
          <Button
            onPress={() => setSearchTerm("")}
            variant="outline"
            size="sm"
            className="rounded-full mt-4"
          >
            <ButtonText>Clear search</ButtonText>
          </Button>
        }
      />
    );
  }

  if (isEmpty) {
    return <EmptyState />;
  }
};

const skeletonData = Array.from({ length: 5 }, (_, index) => ({ id: index }));

export const Classes = () => {
  const [selectedTab, setSelectedTab] = useState<"classes" | "appointment">(
    "classes"
  );
  const [selectedDate, setSelectedDate] = useState(new Date());

  const [searchTerm, setSearchTerm] = useState("");

  const { data = [], isLoading } = useClassesQuery({
    date: formatDate(selectedDate),
  });

  // Reservation feedback handling
  const feedback = useReservationFeedback({
    onMakeAnotherReservation: () => {
      // Refresh the classes list or navigate to a different date
      console.log("Make another reservation");
    },
    onAddToCalendar: async () => {
      // TODO: Implement calendar integration
      console.log("Add to calendar");
    },
    onShareWithFriends: async () => {
      try {
        const shareContent = {
          message: `Check out these amazing fitness classes at YMCA! Join me for a workout.`,
          title: "Join me for a workout!",
        };
        await Share.share(shareContent);
      } catch (error) {
        console.error("Failed to share:", error);
      }
    },
  });

  const filteredData = searchTerm
    ? matchSorter(data, searchTerm, {
        keys: [
          "name",
          "room_name",
          "gym_name",
          "instructor_first_name",
          "instructor_last_name",
          "start_time",
        ],
      })
    : data;

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        <ClassesHeader />
        <ClassesTabs selectedTab={selectedTab} onTabSelect={setSelectedTab} />
        <VStack space="md" className="pb-6">
          <HorizontalDatePicker
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
          />
          <SearchInput onSearch={setSearchTerm} searchTerm={searchTerm} />

          <VStack space="sm" className="px-4 ">
            {isLoading || !filteredData.length ? (
              <RenderEmptyState
                setSearchTerm={setSearchTerm}
                isLoading={isLoading}
                isEmpty={!Boolean(filteredData?.length)}
                searchTerm={searchTerm}
              />
            ) : (
              <FlatList
                data={filteredData}
                renderItem={({ item }) => (
                  <ClassCard
                    key={item.id}
                    {...item}
                    selected_date={formatDate(selectedDate)}
                    onReservationSuccess={feedback.showSuccess}
                    onReservationError={feedback.showError}
                  />
                )}
                keyExtractor={(item) => String(item.id)}
                showsVerticalScrollIndicator={false}
              />
            )}
          </VStack>
        </VStack>
      </VStack>

      {/* Reservation Feedback Components */}
      <ReservationErrorAlert
        isVisible={feedback.isErrorVisible}
        onClose={feedback.hideError}
        message={feedback.errorMessage}
      />

      <ReservationSuccessBottomSheet
        isVisible={feedback.isSuccessVisible}
        onClose={feedback.hideSuccess}
        onMakeAnotherReservation={feedback.handleMakeAnotherReservation}
        onAddToCalendar={feedback.handleAddToCalendar}
        onShareWithFriends={feedback.handleShareWithFriends}
      />
    </SafeAreaView>
  );
};

export default Classes;
