import React, { useState } from 'react';
import { View } from 'react-native';
import { Button, ButtonText } from '@/components/ui/button';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { 
  ReservationErrorAlert, 
  ReservationSuccessBottomSheet 
} from './index';

/**
 * Demo component to showcase the reservation feedback components
 * This can be used for testing and demonstration purposes
 */
export const ReservationFeedbackDemo: React.FC = () => {
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [isSuccessVisible, setIsSuccessVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState('An error just occurred');

  const showError = (message?: string) => {
    setErrorMessage(message || 'An error just occurred');
    setIsErrorVisible(true);
  };

  const hideError = () => {
    setIsErrorVisible(false);
  };

  const showSuccess = () => {
    setIsSuccessVisible(true);
  };

  const hideSuccess = () => {
    setIsSuccessVisible(false);
  };

  const handleMakeAnotherReservation = () => {
    console.log('Make another reservation clicked');
    hideSuccess();
  };

  const handleAddToCalendar = () => {
    console.log('Add to calendar clicked');
    hideSuccess();
  };

  const handleShareWithFriends = () => {
    console.log('Share with friends clicked');
    hideSuccess();
  };

  return (
    <View className="flex-1 bg-white">
      <VStack space="lg" className="p-6 pt-20">
        <Text className="text-2xl font-dm-sans-bold text-center mb-8">
          Reservation Feedback Demo
        </Text>

        <VStack space="md">
          <Button
            onPress={() => showError()}
            className="bg-red-500 rounded-2xl py-4"
            variant="solid"
          >
            <ButtonText className="text-white font-dm-sans-medium">
              Show Default Error
            </ButtonText>
          </Button>

          <Button
            onPress={() => showError('Class reservation failed. Please try again.')}
            className="bg-red-600 rounded-2xl py-4"
            variant="solid"
          >
            <ButtonText className="text-white font-dm-sans-medium">
              Show Custom Error
            </ButtonText>
          </Button>

          <Button
            onPress={showSuccess}
            className="bg-green-500 rounded-2xl py-4"
            variant="solid"
          >
            <ButtonText className="text-white font-dm-sans-medium">
              Show Success Bottom Sheet
            </ButtonText>
          </Button>

          <Button
            onPress={() => {
              setIsErrorVisible(false);
              setIsSuccessVisible(false);
            }}
            className="bg-gray-500 rounded-2xl py-4"
            variant="solid"
          >
            <ButtonText className="text-white font-dm-sans-medium">
              Hide All
            </ButtonText>
          </Button>
        </VStack>
      </VStack>

      {/* Error Alert */}
      <ReservationErrorAlert
        isVisible={isErrorVisible}
        onClose={hideError}
        message={errorMessage}
      />

      {/* Success Bottom Sheet */}
      <ReservationSuccessBottomSheet
        isVisible={isSuccessVisible}
        onClose={hideSuccess}
        onMakeAnotherReservation={handleMakeAnotherReservation}
        onAddToCalendar={handleAddToCalendar}
        onShareWithFriends={handleShareWithFriends}
      />
    </View>
  );
};
