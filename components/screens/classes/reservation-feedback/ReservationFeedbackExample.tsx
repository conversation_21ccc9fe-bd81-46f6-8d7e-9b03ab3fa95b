import React from 'react';
import { View } from 'react-native';
import { router } from 'expo-router';
import { 
  ReservationErrorAlert, 
  ReservationSuccessBottomSheet 
} from './index';
import { useReservationFeedback } from './useReservationFeedback';
import { useReserveClassWithFeedback } from '@/data/screens/classes/mutations/useReserveClassWithFeedback';
import { ClassStatusButton } from '../class-card/class-button';
import { ClassDetailsResponse } from '@/data/screens/classes/types';

interface ReservationFeedbackExampleProps {
  classData: ClassDetailsResponse;
}

/**
 * Example component showing how to integrate reservation feedback components
 * with the existing class reservation system.
 */
export const ReservationFeedbackExample: React.FC<ReservationFeedbackExampleProps> = ({
  classData,
}) => {
  const {
    // Error alert state
    isErrorVisible,
    errorMessage,
    showError,
    hideError,
    
    // Success bottom sheet state
    isSuccessVisible,
    showSuccess,
    hideSuccess,
    
    // Action handlers
    handleMakeAnotherReservation,
    handleAddToCalendar,
    handleShareWithFriends,
  } = useReservationFeedback({
    onMakeAnotherReservation: () => {
      // Navigate to classes list or refresh current view
      console.log('Make another reservation');
    },
    onAddToCalendar: () => {
      // Implement calendar integration
      console.log('Add to calendar');
    },
    onShareWithFriends: () => {
      // Implement sharing functionality
      console.log('Share with friends');
    },
  });

  const { mutate: reserveClass, isPending } = useReserveClassWithFeedback({
    showSuccessFeedback: showSuccess,
    showErrorFeedback: showError,
    onSuccess: (data, variables) => {
      console.log('Reservation successful:', data);
    },
    onError: (error) => {
      console.error('Reservation failed:', error);
    },
  });

  const handleReserve = () => {
    reserveClass({
      class_id: classData.id,
      date: classData.selected_date ?? "",
      is_virtual: Boolean(classData.is_virtual),
    });
  };

  const getButtonStatus = (data: ClassDetailsResponse) => {
    // This should match your existing logic for determining button status
    // For example purposes, returning 'available'
    return 'available' as const;
  };

  return (
    <View className="flex-1">
      {/* Your existing class UI */}
      <View className="p-4">
        <ClassStatusButton
          status={getButtonStatus(classData)}
          onReserve={handleReserve}
          isLoading={isPending}
        />
      </View>

      {/* Error Alert - positioned at the top */}
      <ReservationErrorAlert
        isVisible={isErrorVisible}
        onClose={hideError}
        message={errorMessage}
      />

      {/* Success Bottom Sheet */}
      <ReservationSuccessBottomSheet
        isVisible={isSuccessVisible}
        onClose={hideSuccess}
        onMakeAnotherReservation={handleMakeAnotherReservation}
        onAddToCalendar={handleAddToCalendar}
        onShareWithFriends={handleShareWithFriends}
      />
    </View>
  );
};
