import React from "react";
import { Alert, AlertIcon, AlertText } from "@/components/ui/alert";
import {
  AlertCircleIcon,
  CheckCircleIcon,
  CloseIcon,
} from "@/components/ui/icon";
import { Button, ButtonText } from "@/components/ui/button";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from "@/components/ui/actionsheet";

interface ReservationErrorAlertProps {
  isVisible: boolean;
  onClose: () => void;
  message?: string;
}

interface ReservationSuccessBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onMakeAnotherReservation: () => void;
  onAddToCalendar: () => void;
  onShareWithFriends: () => void;
  className?: string;
}

export const ReservationErrorAlert: React.FC<ReservationErrorAlertProps> = ({
  isVisible,
  onClose,
  message = "An error just occurred",
}) => {
  if (!isVisible) return null;

  return (
    <Alert action="error" variant="solid" className="mx-4 mb-4 rounded-lg">
      <AlertIcon as={AlertCircleIcon} className="text-error-50" />
      <VStack className="flex-1">
        <HStack className="justify-between items-center">
          <AlertText className="text-error-50 font-dm-sans-medium">
            {message}
          </AlertText>
          <Pressable onPress={onClose} className="p-1">
            <CloseIcon className="text-error-50 h-4 w-4" />
          </Pressable>
        </HStack>
      </VStack>
    </Alert>
  );
};

export const ReservationSuccessBottomSheet: React.FC<
  ReservationSuccessBottomSheetProps
> = ({
  isVisible,
  onClose,
  onMakeAnotherReservation,
  onAddToCalendar,
  onShareWithFriends,
  className,
}) => {
  return (
    <Actionsheet isOpen={isVisible} onClose={onClose}>
      <ActionsheetBackdrop />
      <ActionsheetContent className={`bg-white rounded-t-3xl p-6 ${className}`}>
        <ActionsheetDragIndicatorWrapper>
          <ActionsheetDragIndicator />
        </ActionsheetDragIndicatorWrapper>

        <VStack space="lg" className="items-center py-6">
          {/* Success Icon */}
          <VStack className="items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-4">
            <CheckCircleIcon className="text-green-600 h-10 w-10" />
          </VStack>

          {/* Title */}
          <Text className="text-2xl font-dm-sans-bold text-gray-900 text-center">
            Reservation made
          </Text>

          {/* Description */}
          <Text className="text-base font-dm-sans-regular text-gray-600 text-center px-4">
            Your reservation for the selected class has been made successfully.
          </Text>

          {/* Action Buttons */}
          <VStack space="md" className="w-full mt-6">
            {/* Make another reservation button */}
            <Button
              onPress={onMakeAnotherReservation}
              className="w-full bg-[#00B4CC] rounded-2xl py-4"
              variant="solid"
            >
              <ButtonText className="text-white font-dm-sans-medium text-base">
                Make another reservation
              </ButtonText>
            </Button>

            {/* Add to calendar button */}
            <Button
              onPress={onAddToCalendar}
              className="w-full bg-[#E6F9FC] border-2 border-[#00B4CC] rounded-2xl py-4"
              variant="outline"
            >
              <ButtonText className="text-[#00B4CC] font-dm-sans-medium text-base">
                Add to calendar
              </ButtonText>
            </Button>

            {/* Share with friends button */}
            <Button
              onPress={onShareWithFriends}
              className="w-full bg-white border border-gray-300 rounded-2xl py-4"
              variant="outline"
            >
              <ButtonText className="text-gray-700 font-dm-sans-medium text-base">
                Share with friends
              </ButtonText>
            </Button>
          </VStack>
        </VStack>
      </ActionsheetContent>
    </Actionsheet>
  );
};

// Re-export hooks and utilities
export { useReservationFeedback } from "./useReservationFeedback";
export { useReserveClassWithFeedback } from "@/data/screens/classes/mutations/useReserveClassWithFeedback";

// Export types
export type {
  ReservationErrorAlertProps,
  ReservationSuccessBottomSheetProps,
} from "./types";

// Export demo component for testing
export { ReservationFeedbackDemo } from "./Demo";
