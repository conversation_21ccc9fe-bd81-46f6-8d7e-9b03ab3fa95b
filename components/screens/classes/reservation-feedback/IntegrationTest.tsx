import React from 'react';
import { View } from 'react-native';
import { Button, ButtonText } from '@/components/ui/button';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { 
  useReservationFeedback,
  ReservationErrorAlert,
  ReservationSuccessBottomSheet
} from './index';

/**
 * Test component to verify the integration works correctly
 * This simulates the reservation flow with success and error scenarios
 */
export const IntegrationTest: React.FC = () => {
  const feedback = useReservationFeedback({
    onMakeAnotherReservation: () => {
      console.log('✅ Make another reservation clicked');
    },
    onAddToCalendar: () => {
      console.log('✅ Add to calendar clicked');
    },
    onShareWithFriends: () => {
      console.log('✅ Share with friends clicked');
    },
  });

  const simulateSuccessfulReservation = () => {
    console.log('🎯 Simulating successful reservation...');
    feedback.showSuccess();
  };

  const simulateFailedReservation = () => {
    console.log('❌ Simulating failed reservation...');
    feedback.showError('Class is fully booked. Please try another time slot.');
  };

  const simulateNetworkError = () => {
    console.log('🌐 Simulating network error...');
    feedback.showError('Network connection failed. Please check your internet and try again.');
  };

  return (
    <View className="flex-1 bg-white">
      <VStack space="lg" className="p-6 pt-20">
        <Text className="text-2xl font-dm-sans-bold text-center mb-8">
          Reservation Integration Test
        </Text>

        <VStack space="md">
          <Button
            onPress={simulateSuccessfulReservation}
            className="bg-green-500 rounded-2xl py-4"
            variant="solid"
          >
            <ButtonText className="text-white font-dm-sans-medium">
              ✅ Test Successful Reservation
            </ButtonText>
          </Button>

          <Button
            onPress={simulateFailedReservation}
            className="bg-red-500 rounded-2xl py-4"
            variant="solid"
          >
            <ButtonText className="text-white font-dm-sans-medium">
              ❌ Test Failed Reservation
            </ButtonText>
          </Button>

          <Button
            onPress={simulateNetworkError}
            className="bg-orange-500 rounded-2xl py-4"
            variant="solid"
          >
            <ButtonText className="text-white font-dm-sans-medium">
              🌐 Test Network Error
            </ButtonText>
          </Button>

          <Button
            onPress={() => {
              feedback.hideError();
              feedback.hideSuccess();
            }}
            className="bg-gray-500 rounded-2xl py-4"
            variant="solid"
          >
            <ButtonText className="text-white font-dm-sans-medium">
              🔄 Reset All
            </ButtonText>
          </Button>
        </VStack>

        <Text className="text-sm text-gray-600 text-center mt-8">
          Check the console for action logs when interacting with the feedback components.
        </Text>
      </VStack>

      {/* Feedback Components */}
      <ReservationErrorAlert
        isVisible={feedback.isErrorVisible}
        onClose={feedback.hideError}
        message={feedback.errorMessage}
      />

      <ReservationSuccessBottomSheet
        isVisible={feedback.isSuccessVisible}
        onClose={feedback.hideSuccess}
        onMakeAnotherReservation={feedback.handleMakeAnotherReservation}
        onAddToCalendar={feedback.handleAddToCalendar}
        onShareWithFriends={feedback.handleShareWithFriends}
      />
    </View>
  );
};
