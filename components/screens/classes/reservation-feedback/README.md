# Reservation Feedback Components

This module provides UI components and hooks for handling reservation success and error feedback in the YMCA mobile app.

## Components

### ReservationErrorAlert

A red alert component that appears at the top of the screen when a reservation error occurs.

**Props:**
- `isVisible: boolean` - Controls visibility of the alert
- `onClose: () => void` - Callback when user closes the alert
- `message?: string` - Error message to display (defaults to "An error just occurred")

### ReservationSuccessBottomSheet

A bottom sheet that slides up from the bottom when a reservation is successful, providing action buttons for next steps.

**Props:**
- `isVisible: boolean` - Controls visibility of the bottom sheet
- `onClose: () => void` - Callback when user closes the bottom sheet
- `onMakeAnotherReservation: () => void` - Callback for "Make another reservation" button
- `onAddToCalendar: () => void` - Callback for "Add to calendar" button
- `onShareWithFriends: () => void` - Callback for "Share with friends" button
- `className?: string` - Optional additional CSS classes

## Hooks

### useReservationFeedback

A hook that manages the state and handlers for both error and success feedback components.

**Parameters:**
```typescript
{
  onMakeAnotherReservation?: () => void;
  onAddToCalendar?: () => void;
  onShareWithFriends?: () => void;
}
```

**Returns:**
```typescript
{
  // Error alert state
  isErrorVisible: boolean;
  errorMessage: string;
  showError: (message?: string) => void;
  hideError: () => void;
  
  // Success bottom sheet state
  isSuccessVisible: boolean;
  showSuccess: () => void;
  hideSuccess: () => void;
  
  // Action handlers
  handleMakeAnotherReservation: () => void;
  handleAddToCalendar: () => void;
  handleShareWithFriends: () => void;
}
```

### useReserveClassWithFeedback

An enhanced version of the reservation mutation that integrates with the feedback components.

**Parameters:**
```typescript
{
  onSuccess?: (data: any, variables: ReservationEventData) => void;
  onError?: (error: Error) => void;
  showSuccessFeedback?: () => void;
  showErrorFeedback?: (message?: string) => void;
}
```

## Usage Example

```tsx
import React from 'react';
import { View } from 'react-native';
import { 
  ReservationErrorAlert, 
  ReservationSuccessBottomSheet,
  useReservationFeedback 
} from '@/components/screens/classes/reservation-feedback';
import { useReserveClassWithFeedback } from '@/data/screens/classes/mutations/useReserveClassWithFeedback';

export const MyClassComponent = ({ classData }) => {
  const {
    isErrorVisible,
    errorMessage,
    showError,
    hideError,
    isSuccessVisible,
    showSuccess,
    hideSuccess,
    handleMakeAnotherReservation,
    handleAddToCalendar,
    handleShareWithFriends,
  } = useReservationFeedback({
    onMakeAnotherReservation: () => {
      // Navigate to classes list
    },
    onAddToCalendar: () => {
      // Add event to calendar
    },
    onShareWithFriends: () => {
      // Share functionality
    },
  });

  const { mutate: reserveClass, isPending } = useReserveClassWithFeedback({
    showSuccessFeedback: showSuccess,
    showErrorFeedback: showError,
  });

  const handleReserve = () => {
    reserveClass({
      class_id: classData.id,
      date: classData.selected_date,
      is_virtual: classData.is_virtual,
    });
  };

  return (
    <View>
      {/* Your class UI */}
      <Button onPress={handleReserve} disabled={isPending}>
        {isPending ? 'Reserving...' : 'Reserve Class'}
      </Button>

      {/* Feedback components */}
      <ReservationErrorAlert
        isVisible={isErrorVisible}
        onClose={hideError}
        message={errorMessage}
      />

      <ReservationSuccessBottomSheet
        isVisible={isSuccessVisible}
        onClose={hideSuccess}
        onMakeAnotherReservation={handleMakeAnotherReservation}
        onAddToCalendar={handleAddToCalendar}
        onShareWithFriends={handleShareWithFriends}
      />
    </View>
  );
};
```

## Integration with Existing Code

To integrate with your existing class reservation system:

1. Replace `useReserveClass` with `useReserveClassWithFeedback`
2. Add the feedback components to your class screens
3. Use the `useReservationFeedback` hook to manage state
4. Implement the action handlers for calendar and sharing functionality

## Styling

The components use Tailwind CSS classes and follow the existing design system:
- Error alert: Red background with white text and close button
- Success bottom sheet: White background with green success icon and branded buttons
- Buttons follow the existing color scheme (teal/cyan primary color)

## Dependencies

- `@/components/ui/*` - UI component library
- `@tanstack/react-query` - For mutation handling
- `expo-router` - For navigation (if using navigation in action handlers)
