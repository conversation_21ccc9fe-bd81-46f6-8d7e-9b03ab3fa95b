# Integration Guide

This guide shows how to integrate the reservation feedback components with your existing class components.

## Quick Integration

### 1. Update your existing class component

Replace your current reservation handling with the new feedback-enabled version:

```tsx
// Before
import { useReserveClass } from '@/data/screens/classes/mutations/useReserveClass';

const { mutate: reserveClass, isPending } = useReserveClass();

// After
import { 
  useReservationFeedback,
  useReserveClassWithFeedback,
  ReservationErrorAlert,
  ReservationSuccessBottomSheet
} from '@/components/screens/classes/reservation-feedback';

const feedback = useReservationFeedback({
  onMakeAnotherReservation: () => {
    // Navigate to classes or refresh
  },
  onAddToCalendar: () => {
    // Add to calendar
  },
  onShareWithFriends: () => {
    // Share functionality
  },
});

const { mutate: reserveClass, isPending } = useReserveClassWithFeedback({
  showSuccessFeedback: feedback.showSuccess,
  showErrorFeedback: feedback.showError,
});
```

### 2. Add the UI components to your render

```tsx
return (
  <View>
    {/* Your existing class UI */}
    
    {/* Add these at the end */}
    <ReservationErrorAlert
      isVisible={feedback.isErrorVisible}
      onClose={feedback.hideError}
      message={feedback.errorMessage}
    />

    <ReservationSuccessBottomSheet
      isVisible={feedback.isSuccessVisible}
      onClose={feedback.hideSuccess}
      onMakeAnotherReservation={feedback.handleMakeAnotherReservation}
      onAddToCalendar={feedback.handleAddToCalendar}
      onShareWithFriends={feedback.handleShareWithFriends}
    />
  </View>
);
```

## Complete Example

Here's how to update the existing `ClassCard` component:

```tsx
// components/screens/classes/class-card/class-card.tsx
import React from 'react';
import { TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { ClassStatusButton } from './class-button';
import { 
  useReservationFeedback,
  useReserveClassWithFeedback,
  ReservationErrorAlert,
  ReservationSuccessBottomSheet
} from '@/components/screens/classes/reservation-feedback';
import { ClassDetailsResponse } from '@/data/screens/classes/types';
import { getButtonStatus } from './utils'; // Your existing utility

export const ClassCard = (data: ClassDetailsResponse) => {
  const {
    name,
    class_type,
    start_time,
    end_time,
    instructor_first_name,
    instructor_last_name,
    gym_name,
    room_name,
    id,
    selected_date,
  } = data;

  // Reservation feedback handling
  const feedback = useReservationFeedback({
    onMakeAnotherReservation: () => {
      // Could navigate to classes list or refresh current view
      console.log('Make another reservation');
    },
    onAddToCalendar: () => {
      // Implement calendar integration
      console.log('Add to calendar');
    },
    onShareWithFriends: () => {
      // Implement sharing functionality
      console.log('Share with friends');
    },
  });

  const { mutate: reserveClass, isPending } = useReserveClassWithFeedback({
    showSuccessFeedback: feedback.showSuccess,
    showErrorFeedback: feedback.showError,
  });

  const handleCardPress = () =>
    router.push({
      pathname: "/(class-details)/[id]",
      params: { id, date: selected_date },
    });

  const handleReserve = () => {
    reserveClass({
      class_id: id,
      date: selected_date ?? "",
      is_virtual: Boolean(data.is_virtual),
    });
  };

  return (
    <>
      <TouchableOpacity
        onPress={handleCardPress}
        className="bg-white rounded-2xl p-4 mx-4 mb-3 shadow-sm border border-gray-100"
      >
        <VStack space="sm">
          <VStack space="xs">
            <Text className="text-lg font-dm-sans-bold text-typography-900">
              {name}
            </Text>
            <Text className="text-sm font-dm-sans-medium text-typography-700">
              {class_type}
            </Text>
            <Text className="text-sm font-dm-sans-regular text-typography-600">
              {start_time} - {end_time}
            </Text>
          </VStack>

          <HStack className="justify-between items-center mt-2 flex flex-row">
            <VStack space="xs" className="flex gap-1">
              <Text className="text-sm font-dm-sans-regular text-typography-600">
                {instructor_first_name} {instructor_last_name}
              </Text>
              <Text className="text-xs font-dm-sans-regular text-typography-600">
                {`${gym_name}, ${room_name}`}
              </Text>
            </VStack>
            <ClassStatusButton
              onReserve={handleReserve}
              status={getButtonStatus(data)}
              isLoading={isPending}
            />
          </HStack>
        </VStack>
      </TouchableOpacity>

      {/* Feedback Components */}
      <ReservationErrorAlert
        isVisible={feedback.isErrorVisible}
        onClose={feedback.hideError}
        message={feedback.errorMessage}
      />

      <ReservationSuccessBottomSheet
        isVisible={feedback.isSuccessVisible}
        onClose={feedback.hideSuccess}
        onMakeAnotherReservation={feedback.handleMakeAnotherReservation}
        onAddToCalendar={feedback.handleAddToCalendar}
        onShareWithFriends={feedback.handleShareWithFriends}
      />
    </>
  );
};
```

## Action Handlers Implementation

### Calendar Integration
```tsx
const handleAddToCalendar = async () => {
  try {
    // Using expo-calendar or react-native-calendar-events
    const eventConfig = {
      title: `${classData.name} - ${classData.class_type}`,
      startDate: new Date(`${classData.selected_date} ${classData.start_time}`),
      endDate: new Date(`${classData.selected_date} ${classData.end_time}`),
      location: `${classData.gym_name}, ${classData.room_name}`,
      notes: `Instructor: ${classData.instructor_first_name} ${classData.instructor_last_name}`,
    };
    
    // Add to calendar
    await Calendar.createEventAsync(Calendar.DEFAULT, eventConfig);
  } catch (error) {
    console.error('Failed to add to calendar:', error);
  }
};
```

### Share Functionality
```tsx
const handleShareWithFriends = async () => {
  try {
    const shareContent = {
      message: `I just reserved a spot in ${classData.name} at ${classData.gym_name}! Join me on ${classData.selected_date} at ${classData.start_time}.`,
      title: 'Join me for a workout!',
    };
    
    await Share.share(shareContent);
  } catch (error) {
    console.error('Failed to share:', error);
  }
};
```

## Testing

Use the demo component to test the UI:

```tsx
import { ReservationFeedbackDemo } from '@/components/screens/classes/reservation-feedback';

// In your test screen or storybook
<ReservationFeedbackDemo />
```

This will show buttons to trigger both the error alert and success bottom sheet so you can see how they look and behave.
