import { useState, useCallback } from 'react';

interface UseReservationFeedbackReturn {
  // Error alert state
  isErrorVisible: boolean;
  errorMessage: string;
  showError: (message?: string) => void;
  hideError: () => void;
  
  // Success bottom sheet state
  isSuccessVisible: boolean;
  showSuccess: () => void;
  hideSuccess: () => void;
  
  // Action handlers
  handleMakeAnotherReservation: () => void;
  handleAddToCalendar: () => void;
  handleShareWithFriends: () => void;
}

interface UseReservationFeedbackProps {
  onMakeAnotherReservation?: () => void;
  onAddToCalendar?: () => void;
  onShareWithFriends?: () => void;
}

export const useReservationFeedback = ({
  onMakeAnotherReservation,
  onAddToCalendar,
  onShareWithFriends,
}: UseReservationFeedbackProps = {}): UseReservationFeedbackReturn => {
  // Error alert state
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState('An error just occurred');
  
  // Success bottom sheet state
  const [isSuccessVisible, setIsSuccessVisible] = useState(false);

  // Error handlers
  const showError = useCallback((message?: string) => {
    setErrorMessage(message || 'An error just occurred');
    setIsErrorVisible(true);
  }, []);

  const hideError = useCallback(() => {
    setIsErrorVisible(false);
  }, []);

  // Success handlers
  const showSuccess = useCallback(() => {
    setIsSuccessVisible(true);
  }, []);

  const hideSuccess = useCallback(() => {
    setIsSuccessVisible(false);
  }, []);

  // Action handlers
  const handleMakeAnotherReservation = useCallback(() => {
    hideSuccess();
    onMakeAnotherReservation?.();
  }, [hideSuccess, onMakeAnotherReservation]);

  const handleAddToCalendar = useCallback(() => {
    hideSuccess();
    onAddToCalendar?.();
  }, [hideSuccess, onAddToCalendar]);

  const handleShareWithFriends = useCallback(() => {
    hideSuccess();
    onShareWithFriends?.();
  }, [hideSuccess, onShareWithFriends]);

  return {
    // Error alert
    isErrorVisible,
    errorMessage,
    showError,
    hideError,
    
    // Success bottom sheet
    isSuccessVisible,
    showSuccess,
    hideSuccess,
    
    // Action handlers
    handleMakeAnotherReservation,
    handleAddToCalendar,
    handleShareWithFriends,
  };
};
