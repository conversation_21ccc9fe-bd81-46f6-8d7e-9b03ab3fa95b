import { useMutation, useQueryClient } from "@tanstack/react-query";
import { reserveAction, ReservationEventData } from "./useReserveClass";

interface UseReserveClassWithFeedbackProps {
  onSuccess?: (data: any, variables: ReservationEventData) => void;
  onError?: (error: Error) => void;
  showSuccessFeedback?: () => void;
  showErrorFeedback?: (message?: string) => void;
}

export const useReserveClassWithFeedback = ({
  onSuccess,
  onError,
  showSuccessFeedback,
  showErrorFeedback,
}: UseReserveClassWithFeedbackProps = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: reserveAction,
    onSuccess: async (data, variables) => {
      // Invalidate queries to refresh the UI
      queryClient.invalidateQueries({
        queryKey: ["classes", variables.date],
      });

      // Show success feedback
      showSuccessFeedback?.();

      // Call custom success handler
      onSuccess?.(data, variables);
    },

    onError: (error: Error) => {
      console.log('Reservation error:', error.message);
      
      // Show error feedback
      showErrorFeedback?.(error.message);

      // Call custom error handler
      onError?.(error);
    },
  });
};

// Re-export the interface for convenience
export type { ReservationEventData } from "./useReserveClass";
