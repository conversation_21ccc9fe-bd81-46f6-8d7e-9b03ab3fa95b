import { useToaster } from "@/components/screens/classes/class-card/toast";
import { formatDate } from "@/data/common/common.utils";
import { api } from "@/lib/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { uniqueId } from "lodash/fp";

interface ReservationEventData {
  class_id: number;
  date: string;
  is_virtual: boolean;
  type?: string;
}

export const reserveAction = async (data: ReservationEventData) => {
  try {
    const result = await api
      .post("reserve", {
        json: {
          ...data,
          date: formatDate(data.date),
          type: "class",
        },
      })
      .json();

    return result;
  } catch (err) {
    throw new Error(`${err}`);
  }
};

export const useReserveClass = (onSuccess?: () => void) => {
  const toast = useToaster(uniqueId("reserve"));

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: reserveAction,
    onSuccess: async (data, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["classes", variables.date],
      });
    },

    onError: (e) => {
      console.log(e.message);
      toast(e?.message);
    },
  });
};
