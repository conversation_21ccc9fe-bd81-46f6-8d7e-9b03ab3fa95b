{"expo": {"name": "upace-mobile", "slug": "upace-mobile", "version": "0.0.1", "orientation": "portrait", "icon": "./assets/images/appstore.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.upace.mobile"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "softwareKeyboardLayoutMode": "pan", "package": "com.upace.mobile"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/appstore.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-secure-store"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "2a3a8d5a-b1eb-41aa-b1d2-0bd33bab9648"}}}}